# TenantBillPaymentService 重构总结

## 重构目标
优化 `createTenantBillForPayment` 方法，解决以下问题：
1. 分支逻辑复杂（组合支付、汇付支付、非汇付支付等多个分支）
2. 代码重复（每个支付渠道都有类似的费率计算和手续费分摊逻辑）
3. 扩展性差（新增智付支付需要重写很多代码）
4. 费率处理不统一（不同渠道的费率获取和计算方式不一致）

## 重构方案
采用**方案五：简单重构**，通过抽取通用方法来减少重复代码，提高可维护性。

## 重构内容

### 1. 主方法简化
```java
public List<TenantBill> createTenantBillForPayment(Long paymentId) {
    // 数据验证...
    
    // 组合支付
    if (Objects.equals(payment.getTradeType(), TradeTypeEnum.COMBINED_PAY.getDesc())) {
        return handleCombinedPayment(payment);
    }
    
    // 需要手续费的渠道
    if (isChannelNeedFee(payment.getOnlinePayChannel())) {
        return handlePaymentWithFee(payment, paymentItems);
    }
    
    // 普通支付
    return handleNormalPayment(paymentItems);
}
```

### 2. 新增通用方法

#### 2.1 渠道判断方法
```java
private boolean isChannelNeedFee(Integer onlinePayChannel)
```
- 统一判断哪些支付渠道需要计算手续费
- 目前支持：汇付支付、智付支付
- 新增渠道只需在此方法中添加判断条件

#### 2.2 统一手续费处理
```java
private List<TenantBill> handlePaymentWithFee(Payment payment, List<PaymentItem> paymentItems)
```
- 统一处理所有需要手续费的支付渠道
- 调用费率计算和手续费分摊的通用逻辑

#### 2.3 渠道费率计算
```java
private FeeCalculationResult calculateFeeForChannel(Payment payment)
private FeeCalculationResult calculateHuiFuFee(Payment payment)
private FeeCalculationResult calculateDinPayFee(Payment payment)
```
- 根据不同渠道计算费率和总手续费
- 汇付支付：从 HuiFuPayment 表获取 feeAmount
- 智付支付：如果 feeAmount 为空，通过 `金额 * 费率` 计算

#### 2.4 手续费分摊算法
```java
private List<TenantBill> distributeFeeToOrders(Payment payment, List<PaymentItem> paymentItems, FeeCalculationResult feeResult)
```
- 统一的手续费分摊逻辑
- 前 n-1 个订单按费率计算手续费
- 最后一个订单承担剩余手续费，避免精度问题

### 3. 数据结构
```java
private static class FeeCalculationResult {
    private BigDecimal feeRate;      // 费率
    private BigDecimal totalFeeAmount; // 总手续费金额
}
```

## 重构优势

### 1. 易扩展
- 新增支付渠道只需：
  1. 在 `isChannelNeedFee` 方法中添加渠道判断
  2. 在 `calculateFeeForChannel` 方法中添加对应的费率计算逻辑
  3. 实现具体的 `calculateXxxFee` 方法

### 2. 代码复用
- 手续费分摊逻辑完全复用
- 费率计算逻辑模板化
- 减少了约 40 行重复代码

### 3. 职责清晰
- 每个方法职责单一
- 主流程清晰易懂
- 便于单元测试

### 4. 易维护
- 修改某个渠道的逻辑不影响其他渠道
- 通用逻辑集中管理
- 降低了代码复杂度

## 智付支付支持

### 费率处理逻辑
```java
private FeeCalculationResult calculateDinPayFee(Payment payment) {
    BigDecimal feeRate = payment.getFeeRate();
    if (feeRate == null) {
        log.info("智付支付单{}费率为空,需查询费率", payment.getId());
        return null;
    }
    
    // 智付支付如果feeAmount为空，则通过费率计算
    BigDecimal totalFeeAmount = NumberUtil.mul(payment.getTotalPrice(),
            NumberUtil.div(feeRate, NumberConstant.HUNDRED))
            .setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
    
    return new FeeCalculationResult(feeRate, totalFeeAmount);
}
```

### 特点
1. **费率优先**：优先使用 Payment 表中的 feeRate 字段
2. **动态计算**：当 feeAmount 为空时，通过 `总金额 * 费率` 计算总手续费
3. **精度控制**：使用 HALF_UP 舍入模式，保留两位小数
4. **扩展性**：如果后续智付支付有独立的费用记录表，可以轻松修改获取逻辑

## 测试用例
创建了完整的单元测试，覆盖以下场景：
1. 支付单不存在
2. 支付项为空
3. 普通支付（无手续费）
4. 汇付支付（有手续费）
5. 智付支付（通过费率计算手续费）
6. 费率为空的异常情况

## 后续建议

### 1. 组合支付优化
组合支付的逻辑相对复杂，后续可以考虑进一步重构：
- 抽取组合支付的费率计算逻辑
- 统一组合支付和单一支付的处理流程

### 2. 配置化支持
可以考虑将支付渠道的配置信息外部化：
```java
@ConfigurationProperties("payment.channels")
public class PaymentChannelConfig {
    private Map<Integer, ChannelConfig> channels;
    
    public static class ChannelConfig {
        private boolean needFee;
        private String feeSource; // "table" or "calculate"
        private String tableName;
        private String feeField;
    }
}
```

### 3. 监控和日志
- 添加关键业务指标监控
- 完善异常情况的日志记录
- 添加手续费计算的审计日志

## 总结
通过这次重构，成功解决了原有代码的扩展性问题，为智付支付的接入提供了良好的基础。代码结构更加清晰，维护成本显著降低，同时保持了原有功能的完整性。
