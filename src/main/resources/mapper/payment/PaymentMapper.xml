<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.payment.mapper.PaymentMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.payment.model.po.Payment">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="sp_appid" jdbcType="VARCHAR" property="spAppid" />
    <result column="sp_mchid" jdbcType="VARCHAR" property="spMchid" />
    <result column="sup_appid" jdbcType="VARCHAR" property="supAppid" />
    <result column="sub_mchid" jdbcType="VARCHAR" property="subMchid" />
    <result column="payment_no" jdbcType="VARCHAR" property="paymentNo" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="prepay_id" jdbcType="VARCHAR" property="prepayId" />
    <result column="transaction_id" jdbcType="VARCHAR" property="transactionId" />
    <result column="trade_type" jdbcType="VARCHAR" property="tradeType" />
    <result column="trade_state" jdbcType="VARCHAR" property="tradeState" />
    <result column="trade_state_desc" jdbcType="VARCHAR" property="tradeStateDesc" />
    <result column="bank_type" jdbcType="VARCHAR" property="bankType" />
    <result column="sp_openid" jdbcType="VARCHAR" property="spOpenid" />
    <result column="sub_openid" jdbcType="VARCHAR" property="subOpenid" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="success_time" jdbcType="TIMESTAMP" property="successTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="online_pay_channel" jdbcType="INTEGER" property="onlinePayChannel"/>
    <result column="payment_channel_id" jdbcType="BIGINT" property="paymentChannelId"/>
    <result column="fee_rate" property="feeRate"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, store_id, account_id, sp_appid, sp_mchid, sup_appid, sub_mchid, payment_no, 
    total_price, `status`, prepay_id, transaction_id, trade_type, trade_state, trade_state_desc,
    bank_type, sp_openid, sub_openid, finish_time, success_time, update_time, create_time,online_pay_channel, fee_rate, payment_channel_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from payment
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from payment
    where id = #{id,jdbcType=BIGINT} for update
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from payment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.payment.model.po.Payment" useGeneratedKeys="true">
    insert into payment (tenant_id, store_id, account_id, 
      sp_appid, sp_mchid, sup_appid, 
      sub_mchid, payment_no, total_price, 
      `status`, prepay_id, transaction_id, trade_type,
      trade_state, trade_state_desc, bank_type, 
      sp_openid, sub_openid, finish_time, 
      success_time, update_time, create_time,online_pay_channel, fee_rate
      )
    values (#{tenantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, 
      #{spAppid,jdbcType=VARCHAR}, #{spMchid,jdbcType=VARCHAR}, #{supAppid,jdbcType=VARCHAR}, 
      #{subMchid,jdbcType=VARCHAR}, #{paymentNo,jdbcType=VARCHAR}, #{totalPrice,jdbcType=DECIMAL}, 
      #{status,jdbcType=INTEGER}, #{prepayId,jdbcType=VARCHAR}, #{transactionId,jdbcType=VARCHAR},  #{tradeType,jdbcType=VARCHAR}
      #{tradeState,jdbcType=VARCHAR}, #{tradeStateDesc,jdbcType=VARCHAR}, #{bankType,jdbcType=VARCHAR}, 
      #{spOpenid,jdbcType=VARCHAR}, #{subOpenid,jdbcType=VARCHAR}, #{finishTime,jdbcType=TIMESTAMP}, 
      #{successTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP},#{onlinePayChannel}, #{fee_rate}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.payment.model.po.Payment" useGeneratedKeys="true">
    insert into payment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="spAppid != null">
        sp_appid,
      </if>
      <if test="spMchid != null">
        sp_mchid,
      </if>
      <if test="supAppid != null">
        sup_appid,
      </if>
      <if test="subMchid != null">
        sub_mchid,
      </if>
      <if test="paymentNo != null">
        payment_no,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="prepayId != null">
        prepay_id,
      </if>
      <if test="transactionId != null">
        transaction_id,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="tradeState != null">
        trade_state,
      </if>
      <if test="tradeStateDesc != null">
        trade_state_desc,
      </if>
      <if test="bankType != null">
        bank_type,
      </if>
      <if test="spOpenid != null">
        sp_openid,
      </if>
      <if test="subOpenid != null">
        sub_openid,
      </if>
      <if test="finishTime != null">
        finish_time,
      </if>
      <if test="successTime != null">
        success_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="onlinePayChannel != null">
        online_pay_channel,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="spAppid != null">
        #{spAppid,jdbcType=VARCHAR},
      </if>
      <if test="spMchid != null">
        #{spMchid,jdbcType=VARCHAR},
      </if>
      <if test="supAppid != null">
        #{supAppid,jdbcType=VARCHAR},
      </if>
      <if test="subMchid != null">
        #{subMchid,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null">
        #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="prepayId != null">
        #{prepayId,jdbcType=VARCHAR},
      </if>
      <if test="transactionId != null">
        #{transactionId,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="tradeState != null">
        #{tradeState,jdbcType=VARCHAR},
      </if>
      <if test="tradeStateDesc != null">
        #{tradeStateDesc,jdbcType=VARCHAR},
      </if>
      <if test="bankType != null">
        #{bankType,jdbcType=VARCHAR},
      </if>
      <if test="spOpenid != null">
        #{spOpenid,jdbcType=VARCHAR},
      </if>
      <if test="subOpenid != null">
        #{subOpenid,jdbcType=VARCHAR},
      </if>
      <if test="finishTime != null">
        #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="successTime != null">
        #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="onlinePayChannel != null">
        #{onlinePayChannel},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.payment.model.po.Payment">
    update payment
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="spAppid != null">
        sp_appid = #{spAppid,jdbcType=VARCHAR},
      </if>
      <if test="spMchid != null">
        sp_mchid = #{spMchid,jdbcType=VARCHAR},
      </if>
      <if test="supAppid != null">
        sup_appid = #{supAppid,jdbcType=VARCHAR},
      </if>
      <if test="subMchid != null">
        sub_mchid = #{subMchid,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null">
        payment_no = #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="prepayId != null">
        prepay_id = #{prepayId,jdbcType=VARCHAR},
      </if>
      <if test="transactionId != null">
        transaction_id = #{transactionId,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="tradeState != null">
        trade_state = #{tradeState,jdbcType=VARCHAR},
      </if>
      <if test="tradeStateDesc != null">
        trade_state_desc = #{tradeStateDesc,jdbcType=VARCHAR},
      </if>
      <if test="bankType != null">
        bank_type = #{bankType,jdbcType=VARCHAR},
      </if>
      <if test="spOpenid != null">
        sp_openid = #{spOpenid,jdbcType=VARCHAR},
      </if>
      <if test="subOpenid != null">
        sub_openid = #{subOpenid,jdbcType=VARCHAR},
      </if>
      <if test="finishTime != null">
        finish_time = #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="successTime != null">
        success_time = #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="onlinePayChannel != null">
        online_pay_channel = #{onlinePayChannel},
      </if>
      <if test="feeRate != null">
        fee_rate = #{feeRate},
      </if>
      <if test="paymentChannelId != null">
        payment_channel_id = #{paymentChannelId},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateFeeByPrimaryKeyCas" parameterType="com.cosfo.mall.payment.model.po.Payment">
    update payment
    set
        fee_rate = #{feeRate}
    where id = #{id,jdbcType=BIGINT} and fee_rate is null
  </update>

  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.payment.model.po.Payment">
    update payment
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=BIGINT},
      sp_appid = #{spAppid,jdbcType=VARCHAR},
      sp_mchid = #{spMchid,jdbcType=VARCHAR},
      sup_appid = #{supAppid,jdbcType=VARCHAR},
      sub_mchid = #{subMchid,jdbcType=VARCHAR},
      payment_no = #{paymentNo,jdbcType=VARCHAR},
      total_price = #{totalPrice,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=INTEGER},
      prepay_id = #{prepayId,jdbcType=VARCHAR},
      transaction_id = #{transactionId,jdbcType=VARCHAR},
      trade_type = #{tradeType,jdbcType=VARCHAR},
      trade_state = #{tradeState,jdbcType=VARCHAR},
      trade_state_desc = #{tradeStateDesc,jdbcType=VARCHAR},
      bank_type = #{bankType,jdbcType=VARCHAR},
      sp_openid = #{spOpenid,jdbcType=VARCHAR},
      sub_openid = #{subOpenid,jdbcType=VARCHAR},
      finish_time = #{finishTime,jdbcType=TIMESTAMP},
      success_time = #{successTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      online_pay_channel = #{onlinePayChannel}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateStatus">
    update payment
    set status = #{currentStatus}
    <if test="currentStatus == 1">
      ,success_time = NOW()
    </if>
    where id = #{id,jdbcType=BIGINT}
      and status = #{beforeStatus}
  </update>

  <select id="selectByPaymentNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from payment
    where payment_no = #{paymentNo}
  </select>

  <select id="batchQueryByIdsForUpdate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from payment
    where id in
    <foreach collection="ids" item="id" close=")" open="(" separator=",">
      #{id}
    </foreach>
    for update
  </select>

  <select id="querySuccessAndInProcessPaymentsForUpdate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from payment
    where id in
    <foreach collection="ids" item="id" close=")" open="(" separator=",">
      #{id}
    </foreach>
    and status in (1, 4)
    for update
  </select>

    <update id="updateStatusByIds">
        update payment
        set
        status = #{currentStatus}
        where id in
        <foreach collection="ids" item="id" close=")" open="(" separator=",">
            #{id}
        </foreach>
        and status = #{beforeStatus}
    </update>

  <select id="querySuccessPaymentsByTenantId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from payment
    <where>
      status = 1
      and create_time between date_format(#{startTime}, '%Y-%m-%d 00:00:00') and date_format(#{endTime}, '%Y-%m-%d 23:59:59')
      and tenant_id in
      <foreach collection="tenantIds" item="id" close=")" open="(" separator=",">
        #{id}
      </foreach>
    </where>
  </select>
  <select id="querySuccessPayments" resultType="com.cosfo.mall.payment.model.po.Payment">
    select <include refid="Base_Column_List"/>
    from payment
    <where>
      status = 1
      and create_time between date_format(#{startTime}, '%Y-%m-%d 00:00:00') and date_format(#{endTime}, '%Y-%m-%d 23:59:59')
    </where>
  </select>
  <select id="queryDuplicatePayments" resultType="com.cosfo.mall.payment.model.po.Payment">
    select <include refid="Base_Column_List"/>
    from payment
    <where>
      status = 5
      and create_time between date_format(#{startTime}, '%Y-%m-%d 00:00:00') and date_format(#{endTime}, '%Y-%m-%d 23:59:59')
    </where>
  </select>

  <select id="querySuccessPaymentsByTime" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from payment
    <where>
      status = 1
      and success_time <![CDATA[>=]]> #{startTime} and success_time <![CDATA[<]]> #{endTime}
    </where>
  </select>

  <select id="querySuccessByOrderId" resultMap="BaseResultMap">
    select
    p.id, p.tenant_id, p.store_id, p.account_id,
    p.sp_appid, p.sp_mchid, p.sup_appid, p.sub_mchid, p.payment_no,
    p.total_price, p.`status`, p.prepay_id, p.transaction_id,
    p.trade_type, p.trade_state, p.trade_state_desc,
    p.bank_type, p.sp_openid, p.sub_openid, p.finish_time,
    p.success_time, p.update_time, p.create_time,
    p.online_pay_channel, p.fee_rate
    from payment p
    inner join payment_item pi on pi.payment_id = p.id and pi.tenant_id = p.tenant_id
    where p.tenant_id = #{tenantId} and pi.order_id = #{orderId} and p.status = 1
  </select>
</mapper>