<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.payment.mapper.PaymentCombinedDetailMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.payment.model.po.PaymentCombinedDetail">
    <!--@mbg.generated-->
    <!--@Table payment_combined_detail-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="sp_appid" jdbcType="VARCHAR" property="spAppid" />
    <result column="sp_mchid" jdbcType="VARCHAR" property="spMchid" />
    <result column="sup_appid" jdbcType="VARCHAR" property="supAppid" />
    <result column="sub_mchid" jdbcType="VARCHAR" property="subMchid" />
    <result column="master_payment_id" jdbcType="BIGINT" property="masterPaymentId" />
    <result column="master_payment_no" jdbcType="VARCHAR" property="masterPaymentNo" />
    <result column="payment_no" jdbcType="VARCHAR" property="paymentNo" />
    <result column="total_price" jdbcType="DECIMAL" property="totalPrice" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="prepay_id" jdbcType="VARCHAR" property="prepayId" />
    <result column="transaction_id" jdbcType="VARCHAR" property="transactionId" />
    <result column="trade_type" jdbcType="VARCHAR" property="tradeType" />
    <result column="trade_state" jdbcType="VARCHAR" property="tradeState" />
    <result column="trade_state_desc" jdbcType="VARCHAR" property="tradeStateDesc" />
    <result column="bank_type" jdbcType="VARCHAR" property="bankType" />
    <result column="sp_openid" jdbcType="VARCHAR" property="spOpenid" />
    <result column="sub_openid" jdbcType="VARCHAR" property="subOpenid" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="success_time" jdbcType="TIMESTAMP" property="successTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="online_pay_channel" jdbcType="INTEGER" property="onlinePayChannel" />
    <result column="fee_rate" jdbcType="DECIMAL" property="feeRate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_id, store_id, account_id, sp_appid, sp_mchid, sup_appid, sub_mchid, master_payment_id,
    master_payment_no, payment_no, total_price, `status`, prepay_id, transaction_id,
    trade_type, trade_state, trade_state_desc, bank_type, sp_openid, sub_openid, finish_time,
    success_time, create_time, update_time, online_pay_channel, fee_rate
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from payment_combined_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from payment_combined_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.payment.model.po.PaymentCombinedDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into payment_combined_detail (tenant_id, store_id, account_id,
    sp_appid, sp_mchid, sup_appid,
    sub_mchid, master_payment_id, master_payment_no,
    payment_no, total_price, `status`,
    prepay_id, transaction_id, trade_type,
    trade_state, trade_state_desc, bank_type,
    sp_openid, sub_openid, finish_time,
    success_time,
    online_pay_channel, fee_rate)
    values (#{tenantId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT},
    #{spAppid,jdbcType=VARCHAR}, #{spMchid,jdbcType=VARCHAR}, #{supAppid,jdbcType=VARCHAR},
    #{subMchid,jdbcType=VARCHAR}, #{masterPaymentId,jdbcType=BIGINT}, #{masterPaymentNo,jdbcType=VARCHAR},
    #{paymentNo,jdbcType=VARCHAR}, #{totalPrice,jdbcType=DECIMAL}, #{status,jdbcType=INTEGER},
    #{prepayId,jdbcType=VARCHAR}, #{transactionId,jdbcType=VARCHAR}, #{tradeType,jdbcType=VARCHAR},
    #{tradeState,jdbcType=VARCHAR}, #{tradeStateDesc,jdbcType=VARCHAR}, #{bankType,jdbcType=VARCHAR},
    #{spOpenid,jdbcType=VARCHAR}, #{subOpenid,jdbcType=VARCHAR}, #{finishTime,jdbcType=TIMESTAMP},
    #{successTime,jdbcType=TIMESTAMP},
    #{onlinePayChannel,jdbcType=INTEGER}, #{feeRate,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.payment.model.po.PaymentCombinedDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into payment_combined_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="spAppid != null">
        sp_appid,
      </if>
      <if test="spMchid != null">
        sp_mchid,
      </if>
      <if test="supAppid != null">
        sup_appid,
      </if>
      <if test="subMchid != null">
        sub_mchid,
      </if>
      <if test="masterPaymentId != null">
        master_payment_id,
      </if>
      <if test="masterPaymentNo != null">
        master_payment_no,
      </if>
      <if test="paymentNo != null">
        payment_no,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="prepayId != null">
        prepay_id,
      </if>
      <if test="transactionId != null">
        transaction_id,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="tradeState != null">
        trade_state,
      </if>
      <if test="tradeStateDesc != null">
        trade_state_desc,
      </if>
      <if test="bankType != null">
        bank_type,
      </if>
      <if test="spOpenid != null">
        sp_openid,
      </if>
      <if test="subOpenid != null">
        sub_openid,
      </if>
      <if test="finishTime != null">
        finish_time,
      </if>
      <if test="successTime != null">
        success_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="onlinePayChannel != null">
        online_pay_channel,
      </if>
      <if test="feeRate != null">
        fee_rate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="spAppid != null">
        #{spAppid,jdbcType=VARCHAR},
      </if>
      <if test="spMchid != null">
        #{spMchid,jdbcType=VARCHAR},
      </if>
      <if test="supAppid != null">
        #{supAppid,jdbcType=VARCHAR},
      </if>
      <if test="subMchid != null">
        #{subMchid,jdbcType=VARCHAR},
      </if>
      <if test="masterPaymentId != null">
        #{masterPaymentId,jdbcType=BIGINT},
      </if>
      <if test="masterPaymentNo != null">
        #{masterPaymentNo,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null">
        #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="prepayId != null">
        #{prepayId,jdbcType=VARCHAR},
      </if>
      <if test="transactionId != null">
        #{transactionId,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="tradeState != null">
        #{tradeState,jdbcType=VARCHAR},
      </if>
      <if test="tradeStateDesc != null">
        #{tradeStateDesc,jdbcType=VARCHAR},
      </if>
      <if test="bankType != null">
        #{bankType,jdbcType=VARCHAR},
      </if>
      <if test="spOpenid != null">
        #{spOpenid,jdbcType=VARCHAR},
      </if>
      <if test="subOpenid != null">
        #{subOpenid,jdbcType=VARCHAR},
      </if>
      <if test="finishTime != null">
        #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="successTime != null">
        #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="onlinePayChannel != null">
        #{onlinePayChannel,jdbcType=INTEGER},
      </if>
      <if test="feeRate != null">
        #{feeRate,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.payment.model.po.PaymentCombinedDetail">
    <!--@mbg.generated-->
    update payment_combined_detail
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="spAppid != null">
        sp_appid = #{spAppid,jdbcType=VARCHAR},
      </if>
      <if test="spMchid != null">
        sp_mchid = #{spMchid,jdbcType=VARCHAR},
      </if>
      <if test="supAppid != null">
        sup_appid = #{supAppid,jdbcType=VARCHAR},
      </if>
      <if test="subMchid != null">
        sub_mchid = #{subMchid,jdbcType=VARCHAR},
      </if>
      <if test="masterPaymentId != null">
        master_payment_id = #{masterPaymentId,jdbcType=BIGINT},
      </if>
      <if test="masterPaymentNo != null">
        master_payment_no = #{masterPaymentNo,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null">
        payment_no = #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="prepayId != null">
        prepay_id = #{prepayId,jdbcType=VARCHAR},
      </if>
      <if test="transactionId != null">
        transaction_id = #{transactionId,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="tradeState != null">
        trade_state = #{tradeState,jdbcType=VARCHAR},
      </if>
      <if test="tradeStateDesc != null">
        trade_state_desc = #{tradeStateDesc,jdbcType=VARCHAR},
      </if>
      <if test="bankType != null">
        bank_type = #{bankType,jdbcType=VARCHAR},
      </if>
      <if test="spOpenid != null">
        sp_openid = #{spOpenid,jdbcType=VARCHAR},
      </if>
      <if test="subOpenid != null">
        sub_openid = #{subOpenid,jdbcType=VARCHAR},
      </if>
      <if test="finishTime != null">
        finish_time = #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="successTime != null">
        success_time = #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="onlinePayChannel != null">
        online_pay_channel = #{onlinePayChannel,jdbcType=INTEGER},
      </if>
      <if test="feeRate != null">
        fee_rate = #{feeRate,jdbcType=DECIMAL},
      </if>
      <if test="paymentChannelId != null">
        payment_channel_id = #{paymentChannelId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.payment.model.po.PaymentCombinedDetail">
    <!--@mbg.generated-->
    update payment_combined_detail
    set tenant_id = #{tenantId,jdbcType=BIGINT},
    store_id = #{storeId,jdbcType=BIGINT},
    account_id = #{accountId,jdbcType=BIGINT},
    sp_appid = #{spAppid,jdbcType=VARCHAR},
    sp_mchid = #{spMchid,jdbcType=VARCHAR},
    sup_appid = #{supAppid,jdbcType=VARCHAR},
    sub_mchid = #{subMchid,jdbcType=VARCHAR},
    master_payment_id = #{masterPaymentId,jdbcType=BIGINT},
    master_payment_no = #{masterPaymentNo,jdbcType=VARCHAR},
    payment_no = #{paymentNo,jdbcType=VARCHAR},
    total_price = #{totalPrice,jdbcType=DECIMAL},
    `status` = #{status,jdbcType=INTEGER},
    prepay_id = #{prepayId,jdbcType=VARCHAR},
    transaction_id = #{transactionId,jdbcType=VARCHAR},
    trade_type = #{tradeType,jdbcType=VARCHAR},
    trade_state = #{tradeState,jdbcType=VARCHAR},
    trade_state_desc = #{tradeStateDesc,jdbcType=VARCHAR},
    bank_type = #{bankType,jdbcType=VARCHAR},
    sp_openid = #{spOpenid,jdbcType=VARCHAR},
    sub_openid = #{subOpenid,jdbcType=VARCHAR},
    finish_time = #{finishTime,jdbcType=TIMESTAMP},
    success_time = #{successTime,jdbcType=TIMESTAMP},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    online_pay_channel = #{onlinePayChannel,jdbcType=INTEGER},
    fee_rate = #{feeRate,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateStatus">
    update payment_combined_detail
    set status = #{currentStatus}
    <if test="currentStatus == 1">
      ,success_time = NOW()
    </if>
    where id = #{id,jdbcType=BIGINT}
    and status = #{beforeStatus}
  </update>

  <select id="selectByCombinedPaymentNo" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from payment_combined_detail
    where master_payment_no = #{combinePaymentNo,jdbcType=VARCHAR}
  </select>
</mapper>