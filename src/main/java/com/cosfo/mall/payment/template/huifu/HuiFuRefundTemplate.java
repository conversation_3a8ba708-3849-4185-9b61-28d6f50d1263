package com.cosfo.mall.payment.template.huifu;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.common.config.HuiFuConfig;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.context.OutRefundResultEnum;
import com.cosfo.mall.common.context.RefundEnum;
import com.cosfo.mall.common.utils.Global;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.common.utils.TimeUtils;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.dto.HuiFuPaymentRefundQueryResponseDTO;
import com.cosfo.mall.order.model.dto.HuiFuPaymentRefundRequestDTO;
import com.cosfo.mall.order.model.dto.HuiFuPaymentRefundResponseDTO;
import com.cosfo.mall.order.model.dto.HuiFuQueryRefundRequestDTO;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.payment.convert.PaymentConvert;
import com.cosfo.mall.payment.mapper.HuiFuRefundMapper;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.dto.HuifuRefundUpdateDTO;
import com.cosfo.mall.payment.model.po.HuiFuRefund;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.model.request.RefundExecuteRequest;
import com.cosfo.mall.payment.model.request.RefundRequest;
import com.cosfo.mall.payment.model.result.RefundExecuteResult;
import com.cosfo.mall.payment.service.RefundAcctSplitDetailService;
import com.cosfo.mall.payment.service.RefundService;
import com.cosfo.mall.payment.template.RefundTemplate;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.service.TenantAuthConnectionService;
import com.cosfo.mall.wechat.api.PayMchAPI;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * @description: 汇付退款模板
 * @author: George
 * @date: 2023-09-08
 **/
@Slf4j
@Service
public abstract class HuiFuRefundTemplate extends RefundTemplate {

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private RefundAcctSplitDetailService refundAcctSplitDetailService;
    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;
    @Resource
    private HuiFuRefundMapper huiFuRefundMapper;
    @Resource
    private TenantAuthConnectionService tenantAuthConnectionService;
    @Resource
    private RefundService refundService;
    @Resource
    private HuiFuConfig huiFuConfig;
    @Value("${notify-domain}")
    private String notifyDomain;

    /**
     * 创建退款单
     *
     * @param request
     * @return
     */
    @Override
    protected Long createRefundOrder(RefundRequest request) {
        PaymentItem item = paymentItemMapper.selectPaySuccessByOrderId(request.getTenantId(), request.getOrderId());
        Payment payment = paymentMapper.selectByPrimaryKey(item.getPaymentId());

        Refund refund = new Refund();
        String refundNo = Global.generateRefundNo();
        refund.setTenantId(request.getTenantId());
        refund.setAfterSaleId(request.getOrderAfterSaleId());
        refund.setSubMchid(payment.getSpMchid());
        refund.setRefundNo(refundNo);
        refund.setPaymentPrice(payment.getTotalPrice());
        refund.setCreateTime(LocalDateTime.now());
        refund.setRefundStatus(!CollectionUtils.isEmpty(request.getRefundAcctSplitDetailDTOList()) ? RefundEnum.Status.CONFIRM_REFUND.getStatus() : RefundEnum.Status.CREATE_REFUND.getStatus());
        refund.setRefundPrice(request.getRefundPrice());
        refund.setPaymentId(payment.getId());
        refundMapper.insertSelective(refund);
        if (!CollectionUtils.isEmpty(request.getRefundAcctSplitDetailDTOList())) {
            // 插入退款分账明细
            refundAcctSplitDetailService.save(request.getRefundAcctSplitDetailDTOList(), refund.getId());
        }
        return refund.getId();
    }

    @Override
    protected OutRefundResultEnum doLastRefundResult(RefundExecuteRequest request) {
        Refund refund = request.getRefund();
        Payment payment = request.getPayment();
        HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectOne(new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getPaymentId, payment.getId()));
        request.setHuiFuPayment(huiFuPayment);
        HuiFuRefund huiFuRefund = null;
        try {
            LambdaQueryWrapper<HuiFuRefund> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HuiFuRefund::getRefundId, refund.getId()).orderByDesc(HuiFuRefund::getId);
            queryWrapper.last(" limit 1");
            huiFuRefund = huiFuRefundMapper.selectOne(queryWrapper);

            // 序列号为空且汇付结果不存在，则认为是首次发起退款
            if (Objects.isNull(refund.getReqSeqId()) && Objects.isNull(huiFuRefund)) {
                return OutRefundResultEnum.FAIL;
            }

            // 汇付返回的请求时间是最准的，兜底使用退款单创建时间
            Date createTime = TimeUtils.localDateTimeConvertDate(refund.getCreateTime());
            String reqDate = Optional.ofNullable(huiFuRefund).map(HuiFuRefund::getReqDate).orElse(TimeUtils.changeDate2String(createTime, TimeUtils.FORMAT_STRING));
            TenantAuthConnectionDTO authConnectionDTO = tenantAuthConnectionService.queryHistoryPayConfig(huiFuPayment.getTenantId(), huiFuPayment.getHuifuId(), null);

            // 失败单号可重试
            String reqSeqId = refund.getReqSeqId();
            if (StringUtils.isEmpty(reqSeqId)) {
                reqSeqId = Optional.ofNullable(huiFuRefund).map(HuiFuRefund::getReqSeqId).orElse(null);
            }
            if (StringUtils.isEmpty(reqSeqId)) {
                log.info("获取上次退款结果,单号为空避免重复退款不进行重试 refund:{}", JSON.toJSONString(refund));
                return OutRefundResultEnum.PROCESSING;
            }

            // 查询退款结果
            HuiFuQueryRefundRequestDTO huiFuQueryRefundRequestDTO = new HuiFuQueryRefundRequestDTO();
            huiFuQueryRefundRequestDTO.setHuiFuId(huiFuPayment.getHuifuId());
            huiFuQueryRefundRequestDTO.setOrgReqDate(reqDate);
            huiFuQueryRefundRequestDTO.setOrgReqSeqId(reqSeqId);
            HuiFuPaymentRefundQueryResponseDTO huiFuPaymentRefundQueryResponseDTO = PayMchAPI.queryHuiFuPaymentRefundResult(huiFuQueryRefundRequestDTO, authConnectionDTO, huiFuConfig);

            String transStat = Optional.ofNullable(huiFuPaymentRefundQueryResponseDTO).map(HuiFuPaymentRefundQueryResponseDTO::getTrans_stat).orElse(null);
            // 成功,流转业务
            if (Constants.HUIFU_SUCCESS.equals(transStat)) {
                HuiFuPaymentRefundResponseDTO huiFuPaymentRefundResponseDTO = PaymentConvert.convertToHuiFuRefundNotifyDto(huiFuPaymentRefundQueryResponseDTO);
                refundService.huiFuRefundNotify(huiFuPaymentRefundResponseDTO);
                return OutRefundResultEnum.SUCCESS;
            }

            // 明确失败或者单号查询不到退款交易请求
            if (Constants.HUIFU_FAIL.equals(transStat) || Constants.HUIFU_REQ_ID_NOT_EXIST.equals(huiFuPaymentRefundQueryResponseDTO.getResp_code())) {
                log.info("失败或者单号查询不到退款交易请求");
                return OutRefundResultEnum.FAIL;
            }
            log.error("汇付本条退款结果非失败状态不进行重试 refundId:{},huiFuPaymentRefundResponseDTO:{}", refund.getId(), JSON.toJSONString(huiFuPaymentRefundQueryResponseDTO));
        } catch (BizException e) {
            log.warn("汇付查询退款结果异常 huiFuRefund:{},msg:{}", JSON.toJSONString(huiFuRefund), e.getMessage());
        } catch (Exception e) {
            log.error("汇付查询退款结果异常 huiFuRefund:{},e", JSON.toJSONString(huiFuRefund), e);
        }
        return OutRefundResultEnum.PROCESSING;
    }

    @Override
    protected RefundExecuteResult processRefund(RefundExecuteRequest request) {
        Refund refund = request.getRefund();
        String reqSeqId = Global.createHuiFuNo(Global.HUIFU_REFUND);
        boolean success = updateRefundReqIdCas(refund, reqSeqId);
        if (!success) {
            throw new ProviderException("更新汇付退款单请求ID异常，请关注该笔售后单");
        }
        refund.setReqSeqId(reqSeqId);

        HuiFuPayment huiFuPayment = request.getHuiFuPayment();
        TenantAuthConnectionDTO authConnectionDTO = tenantAuthConnectionService.queryHistoryPayConfig(huiFuPayment.getTenantId(), huiFuPayment.getHuifuId(), null);

        // 使用退款单创建时间作为请求退款时间
        Date createTime = TimeUtils.localDateTimeConvertDate(refund.getCreateTime());
        HuiFuPaymentRefundRequestDTO huiFuPaymentRefundRequestDTO = new HuiFuPaymentRefundRequestDTO();
        huiFuPaymentRefundRequestDTO.setReq_date(TimeUtils.changeDate2String(createTime, TimeUtils.FORMAT_STRING));
        huiFuPaymentRefundRequestDTO.setReq_seq_id(reqSeqId);
        huiFuPaymentRefundRequestDTO.setHuifu_id(huiFuPayment.getHuifuId());
        huiFuPaymentRefundRequestDTO.setOrg_req_date(huiFuPayment.getReqDate());
        huiFuPaymentRefundRequestDTO.setOrg_req_seq_id(huiFuPayment.getReqSeqId());
        huiFuPaymentRefundRequestDTO.setOrd_amt(String.format("%.2f", refund.getRefundPrice()));
        huiFuPaymentRefundRequestDTO.setNotify_url(notifyDomain + "/pay-notify/huifu-refund");

        HuiFuRefund huiFuRefund = new HuiFuRefund();
        huiFuRefund.setTenantId(refund.getTenantId());
        huiFuRefund.setRefundId(refund.getId());
        huiFuRefund.setHuifuId(huiFuPayment.getHuifuId());
        huiFuRefund.setReqSeqId(reqSeqId);
        int insert = huiFuRefundMapper.insert(huiFuRefund);
        if (insert <= 0) {
            throw new ProviderException("预先插入汇付退款单请求ID失败，请关注该笔售后单 huiFuRefund" + JSON.toJSONString(huiFuRefund));
        }

        log.info("发起请求退款调用数据 huiFuPaymentRefundRequestDTO：{},refund:{}", JSON.toJSONString(huiFuPaymentRefundRequestDTO), JSON.toJSONString(refund));
        HuiFuPaymentRefundResponseDTO huiFuPaymentRefundResponseDTO = PayMchAPI.HuiFuPaymentRefund(huiFuPaymentRefundRequestDTO, authConnectionDTO, huiFuConfig);

        HuiFuRefund updateHuiFuRefund = new HuiFuRefund();
        updateHuiFuRefund.setId(huiFuRefund.getId());
        updateHuiFuRefund.setHuifuId(huiFuPaymentRefundResponseDTO.getHuifu_id());
        updateHuiFuRefund.setRespCode(huiFuPaymentRefundResponseDTO.getResp_code());
        updateHuiFuRefund.setRespDesc(huiFuPaymentRefundResponseDTO.getResp_desc());
        updateHuiFuRefund.setTransStat(huiFuPaymentRefundResponseDTO.getTrans_stat());
        updateHuiFuRefund.setHfSeqId(huiFuPaymentRefundResponseDTO.getHf_seq_id());
        updateHuiFuRefund.setReqDate(huiFuPaymentRefundResponseDTO.getReq_date());
        updateHuiFuRefund.setReqSeqId(huiFuPaymentRefundResponseDTO.getReq_seq_id());
        updateHuiFuRefund.setLoanFlag(huiFuPaymentRefundResponseDTO.getLoan_flag());
        updateHuiFuRefund.setLoanUndertaker(huiFuPaymentRefundResponseDTO.getLoan_undertaker());
        updateHuiFuRefund.setLoanAcctType(huiFuPaymentRefundResponseDTO.getLoan_acct_type());
        updateHuiFuRefund.setUnconfirmAmt(huiFuPaymentRefundResponseDTO.getUnconfirm_amt());
        updateHuiFuRefund.setConfirmedAmt(huiFuPaymentRefundResponseDTO.getConfirmed_amt());
        updateHuiFuRefund.setOrgReqDate(huiFuPaymentRefundResponseDTO.getOrg_req_date());
        updateHuiFuRefund.setOrgReqSeqId(huiFuPaymentRefundResponseDTO.getOrg_hf_seq_id());
        huiFuRefundMapper.updateById(updateHuiFuRefund);

        String respCode = Optional.ofNullable(huiFuPaymentRefundResponseDTO).map(HuiFuPaymentRefundResponseDTO::getResp_code).orElse(org.apache.commons.lang3.StringUtils.EMPTY);
        // 非正常错误码，日志告警
        if (!Constants.HUIFU_SUCCESS_CODE.equals(respCode) && !Constants.HUIFU_PROCESSING_CODE.equals(respCode)) {
            throw new ProviderException(huiFuPaymentRefundResponseDTO.getResp_desc());
        }
        return RefundExecuteResult.builder().isSuccess(true).build();
    }

    private boolean updateRefundReqIdCas(Refund refund, String reqSeqId) {
        HuifuRefundUpdateDTO huifuRefundUpdateDTO = new HuifuRefundUpdateDTO();
        huifuRefundUpdateDTO.setId(refund.getId());
        huifuRefundUpdateDTO.setNewReqSeqId(reqSeqId);
        huifuRefundUpdateDTO.setReqSeqId(refund.getReqSeqId());
        return refundMapper.updateRefundReqIdCas(huifuRefundUpdateDTO) > 0;
    }

    @Override
    protected void onFailure(RefundExecuteRequest request, RefundExecuteResult result) {

    }

    @Override
    protected void onSuccess(RefundExecuteRequest request, RefundExecuteResult result) {

    }
}
