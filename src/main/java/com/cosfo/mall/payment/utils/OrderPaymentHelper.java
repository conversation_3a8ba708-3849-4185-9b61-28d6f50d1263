package com.cosfo.mall.payment.utils;

import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.mall.common.constants.PayTypeEnum;
import com.cosfo.mall.payment.convert.PaymentConvert;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.vo.PaymentVO;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ParamsException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OrderPaymentHelper {

    @DubboReference
    private OrderQueryProvider orderQueryProvider;

    /**
     * 基于提供的付款值对象生成付款请求。
     * <p>
     * 将与付款请求关联的订单分为两类：零应付价格的订单和非零应付价格的订单。随后，可能会创建两个付款请求：
     *   1. 对于零元订单（如果有）的付款请求。
     *   2. 对于非零元订单的付款请求。
     * 优先处理0元订单,因为0元订单只涉及数据库的操作,不涉及调用第三方接口.也就是说,如果零元订单都处理失败了,
     * 说明数据库出错,那么也没必要去尝试支付非零元订单了
     * 关于组合包订单:
     * 如果涉及了部分0元，部分要付钱的，就走实际支付方式
     * 全部都是0元的，走无需支付
     * </p>
     * @param paymentVO
     * @return
     */
    public List<PaymentRequest> generatePaymentRequests(PaymentVO paymentVO) {
        PaymentRequest request = PaymentConvert.convertToRequest(paymentVO);

        List<String> orderNos = request.getOrderNos();
        if (CollectionUtils.isEmpty(orderNos)) {
            throw new ParamsException("订单编号不能为空");
        }

        List<OrderResp> orders = RpcResultUtil.handle(orderQueryProvider.queryByNos(orderNos));
        List<String> zeroPriceOrders = new ArrayList<>();
        List<String> nonZeroPricedOrders = new ArrayList<>();

        // 非组合包
        for (OrderResp order : orders) {
            if (OrderEnums.OrderType.COMMON.getCode().equals(order.getOrderType()) || OrderEnums.OrderType.PRESALE.getCode().equals(order.getOrderType())) {
                if (BigDecimal.ZERO.compareTo(order.getPayablePrice()) == 0) {
                    zeroPriceOrders.add(order.getOrderNo());
                } else {
                    nonZeroPricedOrders.add(order.getOrderNo());
                }
            }
        }

        // 组合包
        Map<Long, List<OrderResp>> combinedOrders = orders.stream()
                .filter(order -> OrderEnums.OrderType.COMBINE.getCode().equals(order.getOrderType()))
                .collect(Collectors.groupingBy(OrderResp::getCombineOrderId));

        // 判断组合包是否整体为0元
        for (List<OrderResp> combinedOrder : combinedOrders.values()) {
            BigDecimal combinedTotal = combinedOrder.stream().map(OrderResp::getPayablePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            List<String> targetList = combinedTotal.compareTo(BigDecimal.ZERO) == 0 ? zeroPriceOrders : nonZeroPricedOrders;
            combinedOrder.forEach(order -> targetList.add(order.getOrderNo()));
        }

        List<PaymentRequest> requests = new ArrayList<>(2);

        if (!zeroPriceOrders.isEmpty()) {
            PaymentRequest zeroPaymentRequest = PaymentRequest.builder()
                    .orderNos(zeroPriceOrders)
                    .payType(PayTypeEnum.ZERO_PRICE_PAY.getType())
                    .build();
            requests.add(zeroPaymentRequest);
        }

        if (!nonZeroPricedOrders.isEmpty()) {
            request.setOrderNos(nonZeroPricedOrders);
            requests.add(request);
        }
        return requests;
    }
}
