package com.cosfo.mall.payment.model.vo;

import lombok.Data;

import java.util.List;

/**
 * @description: 接收支付请求对象
 * @author: George
 * @date: 2023-09-11
 **/
@Data
public class PaymentVO {

    /**
     * 订单编号
     */
    private List<String> orderNos;
    /**
     * 支付类型 1、微信 2、账期 3、余额 4、支付宝 6、线下支付 7、非现金支付 8、组合支付
     */
    private Integer payType;
    /**
     * 支付类型列表
     * 1、微信 2、账期 3、余额 4、支付宝 6、线下支付 7、非现金支付
     */
    private List<Integer> payTypes;

    /**
     * 线下 支付凭证
     */
    private String paymentReceipt;
    /**
     * 支付渠道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;
    /**
     * 是否H5请求
     */
    private Boolean H5Request = false;
}
