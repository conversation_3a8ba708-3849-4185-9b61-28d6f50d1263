package com.cosfo.mall.payment.model.dto;

import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.Refund;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fansongsong
 * @Date: 2023-07-07
 * @Description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class RefundResultQueryDTO {

    /**
     * 退款单信息
     */
    private Refund refund;

    /**
     * 支付单信息
     */
    private Payment payment;

    /**
     * 证书地址:微信
     */
    private String payCertPath;

    /**
     * 汇付id
     */
    private String huiFuId;
}
