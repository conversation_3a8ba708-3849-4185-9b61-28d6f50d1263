package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.Constants;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleUpdateReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessage;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 自营仓&无仓-退货退款
 * - 退货入库成功消息
 * - 无仓也监听此消息，在上传退货物流后，ofc立即发送
 */
@Slf4j
@Component
@MqListener(topic = "topic_ofc_common_task_finish",
        consumerGroup = MqGroupConstant.GID_OFC_TO_COSFO_DELIVERY4AFTERSALE,
        tag = "tag_ofc_inbound_task"
)
public class OFCDelivery4AfterSaleListener extends AbstractMqListener<CommonFulfillmentFinishMessage> {
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @DubboReference
    private OrderAfterSaleCommandProvider orderAfterSaleCommandProvider;

    @Override
    public void process(CommonFulfillmentFinishMessage msg) {
        log.info("rocketmq 收到OFC消息自营仓退货退款，消息内容：{}", JSONObject.toJSONString(msg));
        String sourceOrderNo = msg.getSourceOrderNo();
        if (StringUtil.isEmpty(sourceOrderNo)) {
            return;
        }
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByNos(Lists.newArrayList(sourceOrderNo)));
        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            log.error("rocketmq 收到OFC消息自营仓退货退款售后订单为空，消息内容：{}", JSONObject.toJSONString(msg));
            return;
        }

        OrderAfterSaleResp afterSaleDTO = afterSaleDTOList.get(0);

        if(!OrderAfterSaleServiceTypeEnum.verifyIsReturnRefund(afterSaleDTO.getServiceType())){
            log.error("rocketmq 收到OFC售后消息，非退货退款类型，不处理，售后信息：{}", JSONObject.toJSONString(afterSaleDTO));
            return;
        }

        OrderAfterSaleUpdateReq update = new OrderAfterSaleUpdateReq();
        update.setId(afterSaleDTO.getId());
        update.setStatus(com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.WAIT_REFUND.getValue());
        update.setRecycleDetails(Constants.NORMAL_RECYCLE_TEMPLATE);
        RpcResultUtil.handle(orderAfterSaleCommandProvider.updateById(update));

        long endTime = System.currentTimeMillis();
        log.info("线程{}：MQ开始回调结束时间：{}ms", Thread.currentThread().getName(), endTime);
    }
}
