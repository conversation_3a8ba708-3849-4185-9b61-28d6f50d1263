package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.mall.order.service.OrderService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.cosfo.FulfillmentOrderResultMessageDTO;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 履约单创建成功
 *  - 更新order表履约单号
 *  - 只有三方仓履约单发此消息需要更新其他数据 实际配送日期和城配仓号
 */
@Slf4j
@Component
@MqListener(topic = "topic_ofc_fulfillment_order_result",
        consumerGroup = MqGroupConstant.GID_OFC_TO_COSFO_ORDER_FULFILLMENT_CREATE,
        tag = "tag_ofc_saas_order"
)
public class OFCFulfillmentOrder4OrderListener extends AbstractMqListener<FulfillmentOrderResultMessageDTO> {
    @Resource
    private OrderService orderService;


    @Override
    public void process(FulfillmentOrderResultMessageDTO msg) {
        log.info("rocketmq 收到OFC履约单创建成功通知消息，消息内容：{}", JSONObject.toJSONString(msg));
        String orderNo = msg.getOrderNo();
        if (StringUtil.isEmpty(orderNo)) {
            return;
        }
        if (!msg.isOfcHandleSuccess()) {
            log.error("rocketmq 收到OFC履约单创建通知消息，处理失败，消息内容：{}", JSONObject.toJSONString(msg));
            return;
        }

        orderService.dealFulfillmentOrderCreate(msg);
    }
}
