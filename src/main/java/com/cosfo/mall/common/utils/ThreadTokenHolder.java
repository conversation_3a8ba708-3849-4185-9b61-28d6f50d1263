package com.cosfo.mall.common.utils;

import com.alibaba.fastjson.JSONArray;
import com.cosfo.mall.common.exception.DefaultServiceException;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import lombok.extern.slf4j.Slf4j;

/**
 * @discription 上下文对象
 * <AUTHOR>
 * @date 2022/5/21 10:12
 */
@Slf4j
public class ThreadTokenHolder {
    private static final ThreadLocal<LoginContextInfoDTO> tokenHolder = new ThreadLocal<>();

    public static LoginContextInfoDTO getLoginContextInfoDTO() {
        return tokenHolder.get();
    }

    public static LoginContextInfoDTO getNoNullToken() {
        LoginContextInfoDTO loginContextInfoDTO = tokenHolder.get();
        if (loginContextInfoDTO == null) {
            throw new DefaultServiceException("用户信息不存在");
        }
        log.error("获取token信息：" + JSONArray.toJSONString(loginContextInfoDTO));
        return loginContextInfoDTO;
    }

    public static void setToken(LoginContextInfoDTO loginContextInfoDTO) {
        tokenHolder.remove();
        tokenHolder.set(loginContextInfoDTO);
    }

    public static void clearMerchantInfo(){
        tokenHolder.remove();
    }
}
