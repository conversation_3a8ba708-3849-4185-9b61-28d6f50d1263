package com.cosfo.mall.tenant.service.impl;

import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import com.cosfo.mall.common.constants.TradeTypeEnum;
import com.cosfo.mall.common.context.TenantBillTypeEnum;
import com.cosfo.mall.common.utils.StringUtils;
import com.cosfo.mall.payment.mapper.HuiFuRefundMapper;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.po.HuiFuRefund;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.repository.HuiFuRefundRepository;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.tenant.model.po.TenantBill;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class TenantBillRefundService {

    @Resource
    private RefundMapper refundMapper;
    @Resource
    private HuiFuRefundMapper huiFuRefundMapper;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @Resource
    private HuiFuRefundRepository huiFuRefundRepository;
    @Resource
    private PaymentService paymentService;

    public TenantBill createTenantBillForRefund(Long refundId) {
        Refund refund = refundMapper.selectByPrimaryKey(refundId);
        if (refund == null) {
            log.error("退款单不存在, refundId: {}", refundId);
            return null;
        }

        if (!StringUtils.isEmpty(refund.getReqSeqId())) {
            return handleHuiFuRefund(refund);
        } else {
            return handleNormalRefund(refund);
        }
    }

    private TenantBill handleHuiFuRefund(Refund refund) {
        HuiFuRefund huiFuRefund = huiFuRefundRepository.queryLastByRefundId(refund.getId());
        if (huiFuRefund == null) {
            log.error("退款单:{}没有汇付退款记录", refund.getId());
            return null;
        }
        TenantBill bill = createBill(refund);

        BigDecimal feeAmount = new BigDecimal(Optional.ofNullable(huiFuRefund.getFeeAmount()).orElse("0"));
        if (BigDecimal.ZERO.compareTo(feeAmount) != 0) {
            bill.setFeeAmount(feeAmount);
        }
        return bill;
    }

    private TenantBill handleNormalRefund(Refund refund) {
        return createBill(refund);
    }


    private TenantBill createBill(Refund refund) {
        Payment payment = paymentService.queryByRefundId(refund.getId());
        if (payment == null) {
            throw new BizException("记录交易流水，未查询到支付单信息");
        }

        TenantBill bill = new TenantBill();
        bill.setType(TenantBillTypeEnum.EXPENSES.getType());
        bill.setTenantId(refund.getTenantId());
        bill.setStoreId(payment.getStoreId());
        bill.setBillPrice(refund.getRefundPrice());
        bill.setPaymentType(TradeTypeEnum.getPayTypeByTradeType(payment.getTradeType()));

        List<OrderAfterSaleResp> afterSaleDTOList = fetchAfterSaleDTOList(refund);

        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            bill.setRecordNo(refund.getRefundNo());
        } else {
            OrderAfterSaleResp afterSaleDTO = afterSaleDTOList.get(0);
            bill.setRecordNo(afterSaleDTO.getAfterSaleOrderNo());
        }
        return bill;
    }

    private List<OrderAfterSaleResp> fetchAfterSaleDTOList(Refund refund) {
        if (refund.getAfterSaleId() == null) {
            return Collections.emptyList();
        }
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(
                orderAfterSaleQueryProvider.queryByIds(Lists.newArrayList(refund.getAfterSaleId())));
        if (CollectionUtils.isEmpty(afterSaleDTOList)) {
            return Collections.emptyList();
        }
        return afterSaleDTOList;
    }

}
