package com.cosfo.mall.tenant.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.OnlinePayChannelEnum;
import com.cosfo.mall.common.constants.TradeTypeEnum;
import com.cosfo.mall.common.context.TenantBillTypeEnum;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.payment.mapper.PaymentCombinedDetailMapper;
import com.cosfo.mall.payment.mapper.PaymentCombinedOrderDetailMapper;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentCombinedDetail;
import com.cosfo.mall.payment.model.po.PaymentCombinedOrderDetail;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.PaymentCombinedOrderDetailService;
import com.cosfo.mall.tenant.model.po.TenantBill;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TenantBillPaymentService {

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @Resource
    private PaymentCombinedDetailService paymentCombinedDetailService;
    @Resource
    private PaymentCombinedOrderDetailMapper paymentCombinedOrderDetailMapper;
    @Resource
    private PaymentCombinedOrderDetailService paymentCombinedOrderDetailService;

    public List<TenantBill> createTenantBillForPayment(Long paymentId) {
        Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
        if (payment == null) {
            log.error("支付单不存在, paymentId: {}", paymentId);
            return Collections.emptyList();
        }

        List<PaymentItem> paymentItems = paymentItemMapper.selectByPaymentId(paymentId);
        if (CollectionUtils.isEmpty(paymentItems)) {
            log.error("支付单:{}没有支付项", paymentId);
            return Collections.emptyList();
        }

        // 组合支付
        if (Objects.equals(payment.getTradeType(), TradeTypeEnum.COMBINED_PAY.getDesc())) {
            return handleCombinedPayment(payment);
        }

        // 需要手续费的渠道
        if (isChannelNeedFee(payment.getOnlinePayChannel())) {
            return handlePaymentWithFee(payment, paymentItems);
        }

        // 普通支付
        return handleNormalPayment(paymentItems);
    }

    /**
     * 生成组合支付交易流水
     *
     * @param payment
     * @return
     */
    private List<TenantBill> handleCombinedPayment(Payment payment) {
        // 查询组合支付明细
        List<PaymentCombinedDetail> combinedDetails = paymentCombinedDetailService.selectByCombinedPaymentNo(payment.getPaymentNo());
        if (CollectionUtils.isEmpty(combinedDetails)) {
            log.error("组合支付单:{}没有组合明细", payment.getId());
            return Collections.emptyList();
        }

        // 收集所有组合明细ID
        List<Long> combinedDetailIds = combinedDetails.stream()
                .map(PaymentCombinedDetail::getId)
                .collect(Collectors.toList());

        // 查询组合支付订单明细
        Long tenantId = payment.getTenantId();
        List<PaymentCombinedOrderDetail> orderDetails = paymentCombinedOrderDetailService.selectByCombinedDetailIds(tenantId, combinedDetailIds);
        if (CollectionUtils.isEmpty(orderDetails)) {
            log.error("组合支付单:{}没有订单明细", payment.getId());
            return Collections.emptyList();
        }

        // 获取费率
        BigDecimal feeRate = payment.getFeeRate();
        if (feeRate == null) {
            log.info("组合支付单{}费率为空,需查询费率", payment.getId());
            return Collections.emptyList();
        }

        // 按组合明细处理手续费
        List<TenantBill> bills = new ArrayList<>();
        for (PaymentCombinedDetail detail : combinedDetails) {
            // 假设余额支付无手续费，仅现结支付有手续费
            if (!OnlinePayChannelEnum.HUIFU_PAY.getChannel().equals(detail.getOnlinePayChannel())) {
                // 余额支付：直接生成无手续费的账单
                List<PaymentCombinedOrderDetail> detailOrders = orderDetails.stream()
                        .filter(od -> od.getCombinedDetailId().equals(detail.getId()))
                        .collect(Collectors.toList());
                bills.addAll(detailOrders.stream()
                        .map(el -> {
                            return createBillForCombinedOrderDetail(el, detail.getTradeType());
                        })
                        .collect(Collectors.toList()));
            } else {
                // 现结支付：计算手续费
                bills.addAll(handleCombinedDetailWithFee(payment, detail, orderDetails, feeRate));
            }
        }

        return bills;
    }

    private List<TenantBill> handleCombinedDetailWithFee(Payment payment, PaymentCombinedDetail detail,
                                                         List<PaymentCombinedOrderDetail> orderDetails, BigDecimal feeRate) {
        // 获取该组合明细的订单
        List<PaymentCombinedOrderDetail> detailOrders = orderDetails.stream()
                .filter(od -> od.getCombinedDetailId().equals(detail.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(detailOrders)) {
            log.error("组合支付明细:{}没有订单明细", detail.getId());
            return Collections.emptyList();
        }
        HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectOne(
                new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getPaymentId, payment.getId()));

        // 获取总手续费（假设payment_combined_detail有feeAmount字段）
        String feeAmount = Optional.ofNullable(huiFuPayment.getFeeAmount()).orElse("0");
        BigDecimal feeAll = new BigDecimal(NumberConstant.ZERO);
        List<TenantBill> bills = new ArrayList<>();

        for (int i = 0; i < detailOrders.size(); i++) {
            PaymentCombinedOrderDetail orderDetail = detailOrders.get(i);
            TenantBill bill = createBillForCombinedOrderDetail(orderDetail, detail.getTradeType());

            if (i != detailOrders.size() - 1) {
                // 非最后一个订单：按费率计算手续费
                BigDecimal fee = NumberUtil.mul(orderDetail.getTotalPrice(),
                                NumberUtil.div(feeRate, NumberConstant.HUNDRED))
                        .setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
                bill.setFeeAmount(fee);
                feeAll = feeAll.add(fee);
                if (feeAll.compareTo(new BigDecimal(feeAmount)) > 0) {
                    log.error("手续费累计大于总手续费,支付单:{}, 组合明细:{}", payment.getId(), detail.getId());
                    throw new ProviderException("手续费累计错误");
                }
            } else {
                // 最后一个订单：用总手续费减去累计值
                bill.setFeeAmount(new BigDecimal(feeAmount).subtract(feeAll));
            }
            bills.add(bill);
        }

        return bills;
    }

    private TenantBill createBillForCombinedOrderDetail(PaymentCombinedOrderDetail orderDetail, String tradeType) {
        // 查询订单信息
        OrderResp order = RpcResultUtil.handle(orderQueryProvider.queryById(orderDetail.getOrderId()));
        TenantBill bill = new TenantBill();
        bill.setType(TenantBillTypeEnum.INCOME.getType());
        bill.setTenantId(order.getTenantId());
        bill.setStoreId(order.getStoreId());
        bill.setBillPrice(orderDetail.getTotalPrice());
        bill.setRecordNo(order.getOrderNo());
        bill.setPaymentType(TradeTypeEnum.getPayTypeByTradeType(tradeType));
        bill.setOnlinePayChannel(order.getOnlinePayChannel());
        bill.setCreateTime(orderDetail.getCreateTime() != null ? orderDetail.getCreateTime() : LocalDateTime.now());
        return bill;
    }

    private List<TenantBill> handleNormalPayment(List<PaymentItem> paymentItems) {
        return paymentItems.stream().map(this::createBill).collect(Collectors.toList());
    }

    private List<TenantBill> handleHuiFuPayment(Payment payment, List<PaymentItem> paymentItems) {
        // 手续费累计
        HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectOne(
                new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getPaymentId, payment.getId()));
        List<Long> orderIds = paymentItems.stream().map(PaymentItem::getOrderId).collect(Collectors.toList());
        List<OrderResp> orders = RpcResultUtil.handle(orderQueryProvider.queryByIds(orderIds));
        BigDecimal feeRate = payment.getFeeRate();
        if (feeRate == null) {
            log.info("支付单{}费率为空,需查询费率", payment);
            return Collections.emptyList();
        }
        String feeAmount = Optional.ofNullable(huiFuPayment.getFeeAmount()).orElse("0");
        BigDecimal feeAll = new BigDecimal(NumberConstant.ZERO);
        List<TenantBill> bills = new ArrayList<>();
        for (int i = 0; i < paymentItems.size(); i++) {
            OrderResp order = orders.get(i);
            TenantBill bill = createBill(paymentItems.get(i));
            if (i != paymentItems.size() - 1) {
                // 非最后一个计算得出手续费
                BigDecimal fee = NumberUtil.mul(order.getTotalPrice(),
                                NumberUtil.div(feeRate, NumberConstant.HUNDRED))
                        .setScale(NumberConstant.TWO, RoundingMode.HALF_UP);
                bill.setFeeAmount(fee);
                feeAll = feeAll.add(fee);
                if (feeAll.compareTo(new BigDecimal(feeAmount)) > 0) {
                    log.error("手续费累计大于总手续费,需关注", new ProviderException());
                }
            } else {
                // 最后一个用总手续费相减
                bill.setFeeAmount(new BigDecimal(feeAmount).subtract(feeAll));
            }
            bills.add(bill);
        }
        return bills;
    }

    private TenantBill createBill(PaymentItem paymentItem) {
        OrderResp order = RpcResultUtil.handle(orderQueryProvider.queryById(paymentItem.getOrderId()));
        TenantBill bill = new TenantBill();
        bill.setType(TenantBillTypeEnum.INCOME.getType());
        bill.setTenantId(order.getTenantId());
        bill.setStoreId(order.getStoreId());
        bill.setBillPrice(paymentItem.getOrderPrice());
        bill.setRecordNo(order.getOrderNo());
        bill.setPaymentType(order.getPayType());
        bill.setOnlinePayChannel(order.getOnlinePayChannel());
        if (null != paymentItem.getCreateTime()) {
            bill.setCreateTime(paymentItem.getCreateTime());
        } else {
            bill.setCreateTime(LocalDateTime.now());
        }

        return bill;
    }
}
