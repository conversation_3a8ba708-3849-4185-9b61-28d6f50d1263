package com.cosfo.mall.tenant.service.impl;

import cn.hutool.core.util.IdUtil;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.RedisKeyEnum;
import com.cosfo.mall.tenant.mapper.TenantBillMapper;
import com.cosfo.mall.tenant.model.po.TenantBill;
import com.cosfo.mall.tenant.service.TenantBillService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2022/5/26  14:23
 */
@Slf4j
@Service
public class TenantBillServiceImpl implements TenantBillService {

    @Resource
    private TenantBillMapper tenantBillMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private TenantBillPaymentService tenantBillPaymentService;
    @Resource
    private TenantBillRefundService tenantBillRefundService;

    @Override
    public void batchInsert(List<TenantBill> bills) {
        if (!CollectionUtils.isEmpty(bills)) {
            bills.forEach(bill -> bill.setBillNo(IdUtil.getSnowflakeNextIdStr()));
            tenantBillMapper.batchInsert(bills);
        }
    }

    @Override
    public void createTenantBillForPayment(Long paymentId) {
        createIdempotentTenantBill("payment", paymentId, RedisKeyEnum.C00009, RedisKeyEnum.C00010, (id) -> {
            List<TenantBill> bills = tenantBillPaymentService.createTenantBillForPayment(id);
            if (CollectionUtils.isEmpty(bills)) {
                return false;
            }

            batchInsert(bills);
            return true;
        });
    }

    @Override
    public void createTenantBillForRefund(Long refundId) {
        createIdempotentTenantBill("refund", refundId, RedisKeyEnum.C00011, RedisKeyEnum.C00012, (id) -> {
            TenantBill bill = tenantBillRefundService.createTenantBillForRefund(id);
            if (bill == null) {
                return false;
            }

            batchInsert(Collections.singletonList(bill));
            return true;
        });
    }

    private void createIdempotentTenantBill(String type, Long id, RedisKeyEnum lockEnum, RedisKeyEnum cacheEnum,
                                            Function<Long, Boolean> billProcessor) {
        String redisKey = lockEnum.join(id.toString());
        RLock lock = redissonClient.getLock(redisKey);
        if (!lock.tryLock()) {
            log.info("生成账单 - 获取锁失败, {}Id: {}", type, id);
            return;
        }
        try {
            String cacheKey = cacheEnum.join(id.toString());
            Object cache = redisTemplate.opsForValue().get(cacheKey);
            if (cache != null) {
                log.info("生成账单 - 已经处理过, {}Id: {}", type, id);
                return;
            }

            // 生成账单
            log.info("开始生成账单, {}Id: {}", type, id);
            Boolean result = billProcessor.apply(id);

            // 生成失败,不设置缓存
            if (!result) {
                log.info("生成账单 - 生成账单失败, {}Id: {}", type, id);
                return;
            }

            redisTemplate.opsForValue().set(cacheKey, String.valueOf(NumberConstant.ONE), NumberConstant.ONE, TimeUnit.DAYS);
            log.info("生成账单 - 生成账单成功, {}Id: {}", type, id);
        } finally {
            lock.unlock();
        }
    }
}
