package com.cosfo.mall.tenant.service.impl;

import com.cosfo.mall.common.constants.OnlinePayChannelEnum;
import com.cosfo.mall.common.constants.TradeTypeEnum;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.service.PaymentCombinedDetailService;
import com.cosfo.mall.payment.service.PaymentCombinedOrderDetailService;
import com.cosfo.mall.tenant.model.po.TenantBill;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.cosfo.ordercenter.client.common.result.RpcResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TenantBillPaymentService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class TenantBillPaymentServiceTest {

    @Mock
    private PaymentMapper paymentMapper;
    
    @Mock
    private PaymentItemMapper paymentItemMapper;
    
    @Mock
    private HuiFuPaymentMapper huiFuPaymentMapper;
    
    @Mock
    private OrderQueryProvider orderQueryProvider;
    
    @Mock
    private PaymentCombinedDetailService paymentCombinedDetailService;
    
    @Mock
    private PaymentCombinedOrderDetailService paymentCombinedOrderDetailService;

    @InjectMocks
    private TenantBillPaymentService tenantBillPaymentService;

    private Payment payment;
    private List<PaymentItem> paymentItems;
    private List<OrderResp> orders;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        payment = new Payment();
        payment.setId(1L);
        payment.setPaymentNo("PAY123456");
        payment.setTotalPrice(new BigDecimal("100.00"));
        payment.setFeeRate(new BigDecimal("0.60")); // 0.6%
        payment.setOnlinePayChannel(OnlinePayChannelEnum.HUIFU_PAY.getChannel());
        payment.setTradeType(TradeTypeEnum.T_JSAPI.getDesc());

        // 准备支付项
        PaymentItem item1 = new PaymentItem();
        item1.setId(1L);
        item1.setOrderId(101L);
        item1.setOrderPrice(new BigDecimal("60.00"));
        item1.setCreateTime(LocalDateTime.now());

        PaymentItem item2 = new PaymentItem();
        item2.setId(2L);
        item2.setOrderId(102L);
        item2.setOrderPrice(new BigDecimal("40.00"));
        item2.setCreateTime(LocalDateTime.now());

        paymentItems = Arrays.asList(item1, item2);

        // 准备订单信息
        OrderResp order1 = new OrderResp();
        order1.setId(101L);
        order1.setOrderNo("ORDER101");
        order1.setTotalPrice(new BigDecimal("60.00"));
        order1.setTenantId(1L);
        order1.setStoreId(1L);
        order1.setPayType(1);
        order1.setOnlinePayChannel(OnlinePayChannelEnum.HUIFU_PAY.getChannel());

        OrderResp order2 = new OrderResp();
        order2.setId(102L);
        order2.setOrderNo("ORDER102");
        order2.setTotalPrice(new BigDecimal("40.00"));
        order2.setTenantId(1L);
        order2.setStoreId(1L);
        order2.setPayType(1);
        order2.setOnlinePayChannel(OnlinePayChannelEnum.HUIFU_PAY.getChannel());

        orders = Arrays.asList(order1, order2);
    }

    @Test
    void testCreateTenantBillForPayment_PaymentNotFound() {
        // Given
        when(paymentMapper.selectByPrimaryKey(1L)).thenReturn(null);

        // When
        List<TenantBill> result = tenantBillPaymentService.createTenantBillForPayment(1L);

        // Then
        assertTrue(result.isEmpty());
        verify(paymentMapper).selectByPrimaryKey(1L);
    }

    @Test
    void testCreateTenantBillForPayment_NoPaymentItems() {
        // Given
        when(paymentMapper.selectByPrimaryKey(1L)).thenReturn(payment);
        when(paymentItemMapper.selectByPaymentId(1L)).thenReturn(Collections.emptyList());

        // When
        List<TenantBill> result = tenantBillPaymentService.createTenantBillForPayment(1L);

        // Then
        assertTrue(result.isEmpty());
        verify(paymentMapper).selectByPrimaryKey(1L);
        verify(paymentItemMapper).selectByPaymentId(1L);
    }

    @Test
    void testCreateTenantBillForPayment_NormalPayment() {
        // Given - 微信支付（不需要手续费）
        payment.setOnlinePayChannel(OnlinePayChannelEnum.WECHAT_PAY.getChannel());
        
        when(paymentMapper.selectByPrimaryKey(1L)).thenReturn(payment);
        when(paymentItemMapper.selectByPaymentId(1L)).thenReturn(paymentItems);
        when(orderQueryProvider.queryById(101L)).thenReturn(RpcResult.success(orders.get(0)));
        when(orderQueryProvider.queryById(102L)).thenReturn(RpcResult.success(orders.get(1)));

        // When
        List<TenantBill> result = tenantBillPaymentService.createTenantBillForPayment(1L);

        // Then
        assertEquals(2, result.size());
        
        // 验证第一个账单
        TenantBill bill1 = result.get(0);
        assertEquals(new BigDecimal("60.00"), bill1.getBillPrice());
        assertEquals("ORDER101", bill1.getRecordNo());
        assertNull(bill1.getFeeAmount()); // 普通支付无手续费
        
        // 验证第二个账单
        TenantBill bill2 = result.get(1);
        assertEquals(new BigDecimal("40.00"), bill2.getBillPrice());
        assertEquals("ORDER102", bill2.getRecordNo());
        assertNull(bill2.getFeeAmount()); // 普通支付无手续费
    }

    @Test
    void testCreateTenantBillForPayment_HuiFuPaymentWithFee() {
        // Given - 汇付支付（需要手续费）
        HuiFuPayment huiFuPayment = new HuiFuPayment();
        huiFuPayment.setFeeAmount("0.60"); // 总手续费0.6元
        
        when(paymentMapper.selectByPrimaryKey(1L)).thenReturn(payment);
        when(paymentItemMapper.selectByPaymentId(1L)).thenReturn(paymentItems);
        when(huiFuPaymentMapper.selectOne(any())).thenReturn(huiFuPayment);
        when(orderQueryProvider.queryByIds(anyList())).thenReturn(RpcResult.success(orders));

        // When
        List<TenantBill> result = tenantBillPaymentService.createTenantBillForPayment(1L);

        // Then
        assertEquals(2, result.size());
        
        // 验证第一个账单（按比例分摊手续费）
        TenantBill bill1 = result.get(0);
        assertEquals(new BigDecimal("60.00"), bill1.getBillPrice());
        assertEquals("ORDER101", bill1.getRecordNo());
        assertEquals(new BigDecimal("0.36"), bill1.getFeeAmount()); // 60 * 0.6% = 0.36
        
        // 验证第二个账单（剩余手续费）
        TenantBill bill2 = result.get(1);
        assertEquals(new BigDecimal("40.00"), bill2.getBillPrice());
        assertEquals("ORDER102", bill2.getRecordNo());
        assertEquals(new BigDecimal("0.24"), bill2.getFeeAmount()); // 0.60 - 0.36 = 0.24
    }

    @Test
    void testCreateTenantBillForPayment_DinPaymentWithFee() {
        // Given - 智付支付（需要手续费，通过费率计算）
        payment.setOnlinePayChannel(OnlinePayChannelEnum.DIN_PAY.getChannel());
        
        when(paymentMapper.selectByPrimaryKey(1L)).thenReturn(payment);
        when(paymentItemMapper.selectByPaymentId(1L)).thenReturn(paymentItems);
        when(orderQueryProvider.queryByIds(anyList())).thenReturn(RpcResult.success(orders));

        // When
        List<TenantBill> result = tenantBillPaymentService.createTenantBillForPayment(1L);

        // Then
        assertEquals(2, result.size());
        
        // 验证第一个账单
        TenantBill bill1 = result.get(0);
        assertEquals(new BigDecimal("60.00"), bill1.getBillPrice());
        assertEquals("ORDER101", bill1.getRecordNo());
        assertEquals(new BigDecimal("0.36"), bill1.getFeeAmount()); // 60 * 0.6% = 0.36
        
        // 验证第二个账单
        TenantBill bill2 = result.get(1);
        assertEquals(new BigDecimal("40.00"), bill2.getBillPrice());
        assertEquals("ORDER102", bill2.getRecordNo());
        assertEquals(new BigDecimal("0.24"), bill2.getFeeAmount()); // 0.60 - 0.36 = 0.24
    }

    @Test
    void testCreateTenantBillForPayment_FeeRateIsNull() {
        // Given - 费率为空的情况
        payment.setFeeRate(null);
        
        when(paymentMapper.selectByPrimaryKey(1L)).thenReturn(payment);
        when(paymentItemMapper.selectByPaymentId(1L)).thenReturn(paymentItems);

        // When
        List<TenantBill> result = tenantBillPaymentService.createTenantBillForPayment(1L);

        // Then
        assertTrue(result.isEmpty()); // 费率为空时返回空列表
    }
}
